/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DragShadowViewTest
 * Description:
 * Version: 1.0
 * Date: 2024/1/30
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/1/30 1.0 create
 */

package com.soundrecorder.browsefile.drag.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Point
import android.os.Build
import android.view.View
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowAppFeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowAppFeatureUtil::class])
class DragShadowViewTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_without_exception_when_create() {
        val view = View(context)
        Whitebox.setInternalState(view, "mRight", 100)
        Whitebox.setInternalState(view, "mLeft", 0)

        Whitebox.setInternalState(view, "mTop", 0)
        Whitebox.setInternalState(view, "mBottom", 100)
        DragShadowView(view, 3, true, 12F, 1.0F)
    }

    @Test
    fun should_without_exception_when_draw_shadow_fun() {
        val view = View(context)
        Whitebox.setInternalState(view, "mRight", 100)
        Whitebox.setInternalState(view, "mLeft", 0)

        Whitebox.setInternalState(view, "mTop", 0)
        Whitebox.setInternalState(view, "mBottom", 100)
        val shadow = DragShadowView(view, 3, true, 12F, 1.0F)

        val canvas = Canvas()
        shadow.onDrawShadow(canvas)
    }

    @Test
    fun should_without_exception_when_provide_metrics_fun() {
        val view = View(context)
        Whitebox.setInternalState(view, "mRight", 100)
        Whitebox.setInternalState(view, "mLeft", 0)

        Whitebox.setInternalState(view, "mTop", 0)
        Whitebox.setInternalState(view, "mBottom", 100)
        val shadow = DragShadowView(view, 3, true, 12F, 1.0F)
        shadow.onProvideShadowMetrics(Point(), Point())
    }
}