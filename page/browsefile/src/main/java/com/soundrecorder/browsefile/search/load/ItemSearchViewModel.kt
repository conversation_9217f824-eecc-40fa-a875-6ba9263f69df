/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseRecord.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.search.load

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertExtendBean
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.CoroutineUtils

open class ItemSearchViewModel : ItemBrowseRecordViewModel() {
    var remove: Boolean = false
    var allContains: Boolean = false
    var titleColorIndex: ArrayList<Int> = ArrayList<Int>()
    var contentColorIndex: ArrayList<Int> = ArrayList<Int>()
    var summaryColorIndex: ArrayList<Int> = ArrayList<Int>()
    var content: String? = null
    var searchValue: String? = null
    var summaryText: String? = null

    /*dmp center extension,for future change*/
    var extend: SearchInsertExtendBean? = null

    /*get result by center-dmp*/
    var isFromCenter: Boolean = false
    var isMatchTitle: Boolean = false
    var isMatchSummary: Boolean = false
    var isMatchRecord: Boolean = true
    private var isFileExist: Boolean? = null

    fun isFileExist(): Boolean = CoroutineUtils.safeCheck({
        if ((isFileExist == null) || (isFileExist == true)) {
            try {
                isFileExist =
                    FileUtils.isFileExist(MediaDBUtils.genUri(mediaId))
            } catch (e: Exception) {
                isFileExist = false
                DebugUtil.e(TAG, "isFileExist error")
            }
        }
        isFileExist ?: false
    }, false)

    override fun toString(): String {
        return "ItemSearchViewModel DetailInfo:(allContains=$allContains, " +
                "titleColorIndex=$titleColorIndex, contentColorIndex=$contentColorIndex, " +
                "content=$content, searchValue=$searchValue, isFromCenter=$isFromCenter, " +
                "nodeId=$noteId, recordUUID=$recordUUID, summart=$summaryText, " +
                "title=$title, displayName=$displayName, mediaId=$mediaId)"
    }

    override fun toStartPlayModel(
        isFromOtherApp: Boolean,
        seekToMill: Long?,
        autoPlay: Boolean?,
        isRecycle: Boolean
    ): StartPlayModel {
        return super.toStartPlayModel(isFromOtherApp, seekToMill, autoPlay, isRecycle).apply {
            this.isFromSearch = true
            this.browseSearchWord = searchValue ?: ""
        }
    }
}