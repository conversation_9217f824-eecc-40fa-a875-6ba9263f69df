/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordItemDragDelete
 * Description:
 * Version: 1.0
 * Date: 2024/2/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/18 1.0 create
 */

package com.soundrecorder.browsefile.home

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.BitmapDrawable
import android.view.DragEvent
import android.view.HapticFeedbackConstants
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.drag.RecordFileDragHelper
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RecordItemDragDelegate(val context: Context?, val taskId: Int) {
    private val logTag = "RecordItemDragDelegate"
    /*是否拖拽到其他应用*/
    private var dragOutSide = false
    /*录音接受录音自己文件Drop事件的event.hash*/
    private var recordDropHash: Int? = null
    private var refreshDataWhenDragEnd = false


    fun checkIsDraggingWhenRefreshData(): Boolean {
        if (ItemBrowseRecordViewModel.liveDragMode[taskId]?.value == true) {
            refreshDataWhenDragEnd = true
            DebugUtil.d(logTag, "checkIsDraggingState true")
            return true
        }
        return false
    }

    fun setReceiveDragListener(view: View?) {
        view?.setOnDragListener { view, dragEvent ->
            return@setOnDragListener when (dragEvent.action) {
                DragEvent.ACTION_DROP -> {
                    DebugUtil.d(logTag, "setReceiveDragListener,ACTION_DROP localState=${dragEvent.localState}")
                    if (dragEvent.localState == RecordFileDragHelper.DRAG_LOCAL_STATE) {
                        // 当前拖拽数据是录音的，记录hash值，便于拖拽结束后过滤该场景
                        recordDropHash = dragEvent.hashCode()
                    }
                    // return false:录音不接收其他应用任何形式的文件;
                    false
                }
                DragEvent.ACTION_DRAG_STARTED -> true
                DragEvent.ACTION_DRAG_ENTERED -> true
                DragEvent.ACTION_DRAG_LOCATION -> true
                DragEvent.ACTION_DRAG_EXITED -> true
                DragEvent.ACTION_DRAG_ENDED -> true
                else -> false
            }
        }
    }

    fun tryStartDrag(dragView: View?, vibrate: Boolean, dragEnd: ((refreshData: Boolean) -> Unit)? = null) {
        DebugUtil.i(logTag, "tryStartDrag ${ItemBrowseRecordViewModel.liveDragMode[taskId]?.value}")
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value != true) {
            DebugUtil.w(logTag, "tryStartDrag return not editMode")
            return
        }

        val context = context ?: return
        val view = dragView ?: return
        val selectList = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.elements()?.toList() ?: return

        if (RecordFileDragHelper.checkCanDragAndDrop(context, selectList)) {
            RecordFileDragHelper.startDragAndDrop(context, view, selectList) {
                if (it) {
                    if (vibrate) {
                        (context as? AppCompatActivity)?.lifecycleScope?.launch(Dispatchers.Main) {
                            view.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                        }
                    }
                    ItemBrowseRecordViewModel.liveDragMode[taskId]?.postValue(true)
                } else {
                    view.setOnDragListener(null)
                }
            }

            view.setOnDragListener { v, event ->
                when (event.action) {
                    DragEvent.ACTION_DRAG_ENTERED -> dragOutSide = false // 拖拽进入view的边界
                    DragEvent.ACTION_DRAG_EXITED -> dragOutSide = true // 拖拽离开该view的边界
                    // 拖拽结束
                    DragEvent.ACTION_DRAG_ENDED -> {
                        ItemBrowseRecordViewModel.liveDragMode[taskId]?.value = false
                        v.setOnDragListener(null)
                        handleDragResult(event, selectList.size)
                        dragEnd?.invoke(refreshDataWhenDragEnd)
                        refreshDataWhenDragEnd = false
                    }
                }
                true
            }
        }
    }

    /**
     * 处理拖拽结束结果（拖拽到三方应用成功：上报埋点； 失败：弹toast）
     * dragOutSide：true 用于判断有拖拽移动行为（拦截长按后拖拽立即松手(当前页面松手)，默认为false，此时不做处理）
     * recordDropHash != event.hashCode()：用于拦截拖拽在当前页面松手，此种场景不处理
     */
    private fun handleDragResult(event: DragEvent, fileCount: Int) {
        DebugUtil.i(
            logTag,
            "handleDragResult result=${event.result},dragOutSide=$dragOutSide, isFromActionDrop: ${recordDropHash == event.hashCode()}")
        if (dragOutSide) {
            dragOutSide = false
            if (recordDropHash != event.hashCode()) {
                if (!event.result) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.tip_file_drag_no_drag_in
                    )
                } else {
                    addDragSuccessEvent(fileCount)
                }
            }
        }
    }

    private fun addDragSuccessEvent(fileCount: Int) {
        val eventInfo = HashMap<String, String>()
        eventInfo[RecorderUserAction.KEY_FILE_DRAG_SUCCESS_COUNT] = fileCount.toString()
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(),
            RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
            RecorderUserAction.EVENT_ID_FILE_DRAG_SUCCESS,
            eventInfo,
            false)
    }

    companion object {
        /*长按item，延迟200ms触发拖拽*/
        const val DURATION_DRAG_DELAY_DEFAULT = 200L
        /*进入编辑模式，录音动效总时长*/
        const val DURATION_ENTER_EDIT_MODE_ANIM = 347L + DURATION_DRAG_DELAY_DEFAULT
        /*checkBox 勾选框动效时长*/
        const val DURATION_CHECKBOX_ANIM = 120L + DURATION_DRAG_DELAY_DEFAULT
        private const val ALPHA_ITEM_DRAGGING = 90

        @JvmStatic
        fun setViewAlpha(view: View) {
            kotlin.runCatching {
                val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)
                val paint = Paint()
                paint.color = view.context.getColor(com.soundrecorder.common.R.color.color_white_auto_dark)
                paint.alpha = ALPHA_ITEM_DRAGGING
                canvas.drawRect(0f, 0f, view.width.toFloat(), view.height.toFloat(), paint)
                val drawable = BitmapDrawable(view.resources, bitmap)
                view.foreground = drawable
            }.onFailure {
                DebugUtil.e("RecordItemDragDelegate", "setViewAlpha error $it")
            }
        }
    }
}