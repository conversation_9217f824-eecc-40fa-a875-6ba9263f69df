/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClipResultCode
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord;

/**
 * 裁切结果的code
 */
public final class ClipResultCode {

    /**
     * 裁切成功
     */
    public static final int CLIP_SUCCESS = 200;
    /**
     * 打开裁切原始文件失败
     */
    public static final int CLIP_OPEN_SOURCE_FILE_FAIL = 100;

    /**
     * 生成裁切目标文件失败
     */
    public static final int CLIP_GEN_DEST_FILE_FAIL = 101;

    /**
     * 打开裁切目标文件失败
     */
    public static final int CLIP_OPEN_DEST_FILE_FAIL = 102;

    /**
     * 获取音轨失败
     */
    public static final int CLIP_AUDIO_TRACK_FAIL = 103;

    /**
     * 获取到音频样本的错误size
     */
    public static final int CLIP_SAMPLE_SIZE_ERROR = 104;

    /**
     * 获取到音频轨道index的错误size
     */
    public static final int CLIP_SAMPLE_TRACK_INDEX_ERROR = 107;

    /**
     * 裁切音频文件时io写失败
     */
    public static final int CLIP_WRITE_FAIL = 105;

    /**
     * 裁切失败
     */
    public static final int CLIP_ERROR = 106;


    /**
     * 裁切的时间范围选择错误
     */
    public static final int CLIP_TIME_RANGE_ERROR = 201;

    /**
     * 裁切被取消
     */
    public static final int CLIP_IS_CANCELED = 202;


    public static boolean isClipSuccess(int code) {
        return CLIP_SUCCESS == code;
    }
}
