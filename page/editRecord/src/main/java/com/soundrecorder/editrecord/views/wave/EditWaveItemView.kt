/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RegionSelectListener
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: pingyong<PERSON>@oppo.com
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.wave

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.util.AttributeSet
import android.view.ViewParent
import com.soundrecorder.editrecord.R
import com.soundrecorder.editrecord.views.region.RegionContextImp
import com.soundrecorder.editrecord.views.region.RegionCutStrategyImp
import com.soundrecorder.editrecord.views.wave.interfaze.ISubscriber
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView

class EditWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) :
    WaveItemView(context, attrs, defStyle), ISubscriber {

    companion object {
        const val ADD = 1
        const val RESET = 2

        const val NONE = 0
        const val RIGHT = 1
        const val LEFT = 2

        const val FLOAT_NUMBER_0 = 0f
        const val FLOAT_TWO = 2f

        private const val GAP_LINE_WHITE = 4
        private const val GAP_LINE_SPACE = 9

        @JvmField
        var sDelRadio = 0f
    }

    private var mHandler: WaveItemHandler? = null
    private var mCutRegionContextImp: RegionContextImp? = null

    private var mCutLineTop = 0f
    private var mCutLineBottom = 0f
    private var mHandlerWidth = 0

    private var mLastLinePaint: Paint? = null

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun initTypeAttr(context: Context, array: TypedArray) {
        super.initTypeAttr(context, array)

        mHandler = WaveItemHandler(this)
        mCutRegionContextImp = RegionContextImp(RegionCutStrategyImp(array, this, null))
    }

    override fun initBasicInfo(context: Context) {
        super.initBasicInfo(context)
        mHandlerWidth = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp10)
        mCutLineTop = mMarkViewHeight.toFloat()
        mCutLineBottom = mViewHeight
    }

    override fun initPaint(context: Context) {
        super.initPaint(context)

        mLastLinePaint = Paint().apply {
            strokeCap = Paint.Cap.ROUND
            style = Paint.Style.STROKE
            isAntiAlias = true
            color = context.getColor(R.color.edit_last_time_line_color)
            strokeWidth = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp1).toFloat()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        mCutLineBottom = mViewHeight
        mCutRegionContextImp?.setItemViewHeight(mViewHeight)
    }

    override fun onDraw(canvas: Canvas) {
        //绘制刻度尺和时间文字
        drawRuler(canvas)
        //绘制录制/播放波形
        drawAmplitude(canvas)
        //绘制标记
        drawBookmark(canvas)
        //绘制裁切内容
        drawLastTime(canvas)
    }

    override fun drawFirstItemAmplitude(canvas: Canvas?) {
        //do nothing
    }

    override fun drawDottedLine(canvas: Canvas?, isRTL: Boolean, currentX: Float, viewWidth: Int) {
        //do nothing
    }

    private fun drawLastTime(canvas: Canvas) {
        mCutRegionContextImp?.onDraw(canvas)
        if (mViewIndex == 0) {
            return
        }
        if (parent is EditWaveRecyclerView) {
            val lastTime = (parent as EditWaveRecyclerView).getLastTime()
            if (lastTime != null) {
                val startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
                val endTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
                if (startTime <= lastTime && endTime > lastTime) {
                    var x = getXByTime(lastTime)
                    mLastLinePaint?.let {
                        it.pathEffect = DashPathEffect(
                            floatArrayOf(
                                GAP_LINE_WHITE.toFloat(),
                                GAP_LINE_SPACE.toFloat()
                            ), 0f
                        )
                        if (isReverseLayout) {
                            x = width - x
                        }
                        canvas.drawLine(x, mCutLineTop, x, mCutLineBottom, it)
                    }
                }
            }
        }
    }

    override fun notifyStartTime() {
        notifyTime()
    }

    override fun notifyEndTime() {
        notifyTime()
    }

    private fun notifyTime() {
        postInvalidateOnAnimation()
    }

    override fun notifyTimeChangeByX(x: Float) {
        val loc = IntArray(2)
        getLocationInWindow(loc)
        var locX = x - loc[0]
        if (isReverseLayout) {
            locX = loc[0] + width - x
        }
        if (locX < FLOAT_NUMBER_0 || locX > width) {
            return
        }
        val parent: ViewParent = parent ?: return
        if (parent is EditWaveRecyclerView) {
            var time = getTimeByLocX(locX)
            if (time < 0) {
                time = 0
            }
            val totalTime = parent.totalTime
            if (time > totalTime) {
                time = totalTime
            }

            when (parent.hitStatus) {
                LEFT -> {
                    parent.endRecord?.let {
                        if (time > it - TIME_GAP) {
                            time = it - TIME_GAP
                        }
                    }
                    if (time > totalTime - TIME_GAP) {
                        time = if (totalTime - TIME_GAP < 0) 0 else totalTime - TIME_GAP
                    }
                    time = if (mViewIndex == 0) 0 else time
                    if (time < 0) {
                        time = 0
                    }
                    parent.setCutStartTime(time)
                    parent.handlerMoveListener?.onStartMove(time)
                }
                RIGHT -> {
                    parent.startRecord?.let {
                        if (time < it + TIME_GAP) {
                            time = it + TIME_GAP
                        }
                    }
                    if (time > totalTime) {
                        time = totalTime
                    }
                    parent.setCutEndTime(time)
                    parent.handlerMoveListener?.onEndMove(time)
                }
            }
            postInvalidateOnAnimation()
        }
    }

    private fun getTimeByLocX(locX: Float): Long {
        if (mViewIndex == 0) {
            // 当前为波形的第一个ITEM，直接return 0
            return 0
        }
        val locTime = locX / mPxPerMs
        val startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
        val result = (startTime + locTime).toLong()
        return if (result < 0) startTime else result
    }

    override fun notifyUp() {
        mHandler?.sendEmptyMessage(RESET)
        postInvalidateOnAnimation()
    }

    override fun isTouchHandler(x: Float): Boolean {
        val parent = parent ?: return false
        if (parent is EditWaveRecyclerView) {
            val startRecord = parent.startRecord
            val endRecord = parent.endRecord
            val isStart = startRecord != null && isTouchHandler(x, startRecord)
            val isEnd = endRecord != null && isTouchHandler(x, endRecord)
            when {
                isStart -> parent.hitStatus = LEFT
                isEnd -> parent.hitStatus = RIGHT
                else -> parent.hitStatus = NONE
            }
            return isStart || isEnd
        }
        return false
    }

    private fun isTouchHandler(x: Float, time: Long): Boolean {
        var tagX = getXByTime(time)
        if (isReverseLayout) {
            tagX = width - tagX
        }
        val loc = IntArray(2)
        getLocationInWindow(loc)
        val locX = x - loc[0]
        return (tagX - (mHandlerWidth shr 1) < locX
                && tagX + (mHandlerWidth shr 1) > locX)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mHandler?.removeCallbacksAndMessages(null)
        mHandler = null
    }

    override
    fun setCurViewIndex(index: Int) {
        super.setCurViewIndex(index)
        mCutRegionContextImp?.setCurViewIndex(mViewIndex)
    }
}

/**
 * To watch  reset cut time and tell Globlepreview to do some something
 * change;
 */
interface HandlerMoveListener {
    fun onStartMove(time: Long)
    fun onEndMove(time: Long)
}