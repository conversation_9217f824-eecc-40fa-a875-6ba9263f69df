<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentTextLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.soundrecorder.playback.newconvert.view.BackgroundTextView
        android:id="@+id/contentTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:paddingTop="0dp"
        android:paddingBottom="@dimen/dp16"
        android:lineSpacingMultiplier="1.55"
        android:textColor="@color/coui_color_label_primary"
        android:textSize="@dimen/sp16"
        android:includeFontPadding="false"
        android:textIsSelectable="true"
        android:textDirection="ltr"
        android:background="@color/coui_transparence"
        app:background_color="?attr/couiColorPrimary"
        android:text=""
        tools:text="abcd"/>

</LinearLayout>