<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.soundrecorder.playback.newconvert.exportconvert.txt.ShareWithTxtActivity">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container_view"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
<!--    app:layout_constraintTop_toBottomOf="@id/appbar_layout"-->

<!--    <com.google.android.material.appbar.AppBarLayout-->
<!--        android:id="@+id/appbar_layout"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:background="@null"-->
<!--        android:paddingTop="@dimen/dp32"-->
<!--        app:elevation="0dp">-->

<!--        <com.coui.appcompat.toolbar.COUIToolbar-->
<!--            android:id="@+id/toolbar"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:minHeight="@dimen/toolbar_min_height" />-->

<!--    </com.google.android.material.appbar.AppBarLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>