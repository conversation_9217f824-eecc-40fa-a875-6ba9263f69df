<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sv_convert_init"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:paddingStart="@dimen/responsive_ui_margin_large"
    android:paddingEnd="@dimen/responsive_ui_margin_large"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_convert_init"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.55" />

        <com.soundrecorder.common.widget.OSImageView
            android:id="@+id/img_init_convert"
            android:layout_width="@dimen/os_image_def_width"
            android:layout_height="@dimen/os_image_def_height"
            android:importantForAccessibility="no"
            app:anim_end_frame="240"
            app:anim_frame_duration="240"
            app:anim_frame_rate="60"
            app:anim_start_frame="0"
            app:img_draw="@drawable/icon_convert_init"
            app:layout_constraintBottom_toTopOf="@id/guide_convert_init"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/ll_convert_init"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guide_convert_init">

            <TextView
                android:id="@+id/tv_convert_init_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp40"
                android:layout_marginTop="@dimen/dp15"
                android:layout_marginEnd="@dimen/dp40"
                android:gravity="center"
                android:text="@string/transfer_dialog_statement_title_new"
                android:textColor="@color/coui_color_primary_neutral"
                style="@style/couiTextAppearanceHeadline4"/>

            <TextView
                android:id="@+id/tv_convert_init_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp40"
                android:layout_marginTop="@dimen/dp6"
                android:layout_marginEnd="@dimen/dp40"
                android:gravity="center"
                android:text="@string/convert_init_description"
                android:textColor="@color/coui_color_secondary_neutral"
                style="@style/couiTextAppearanceBody"/>

            <TextView
                android:id="@+id/tv_start_convert"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:fontFamily="sans-serif-medium"
                android:lineSpacingExtra="@dimen/sp4"
                android:paddingStart="@dimen/dp12"
                android:paddingTop="@dimen/dp4"
                android:paddingEnd="@dimen/dp12"
                android:paddingBottom="@dimen/dp4"
                android:text="@string/start_transfer"
                android:textColor="?attr/couiColorPrimaryText"
                android:textSize="@dimen/dp14" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
