<template>
  <div class="content"></div>
</template>
<script>
import { formatTime } from '../../common/utils.js'
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      drawComplete: false
    }
  },
  computed: {
    typeText() {
      if (this.type === 'abstract') {
         return 'name:summary_create_dian_new'   //摘要生成中
      }
      return 'name:transfering'   // 正在转写
    },
  },

}
</script>

<style lang="less">
.content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  /* justify-content: space-between;  */
  .progress {
    opacity: 0px;
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    background-repeat: no-repeat;
    background-size: 15px;
    background-position: center;
    .canvas {
      width: 40px;
      height: 40px;
      align-self: center;
      background-color: transparent;
    }
  }
}
</style>
