<import name="widget-icon" src="../icon/index.ux"></import>

<template>
  <div
    class="widgetui-btn"
    forcedark="false"
    style="{{ formatStyle.buttonStyle }}"
  >
    <div
      if="{{ image.iconPath || image.icon }}"
      style="{{ formatStyle.iconStyle }}"
      class="widgetui-btn-icon"
    >
      <image
        if="{{ image.iconPath && !image.icon }}"
        src="{{ image.iconPath }}"
        enablenightmode="false"
      ></image>

      <widget-icon
        if="{{ image.icon }}"
        type="{{ image.icon.type }}"
        size="{{ size === 'small' ? 16 : 20 }}"
        color="{{ image.icon.color }}"
      ></widget-icon>
    </div>
    <text
      style="{{ formatStyle.textStyle }}"
      class="widgetui-btn-text"
      forcedark="false"
    >
      <slot></slot>
    </text>
    <div class="widgetui-btn-mask"></div>
  </div>
</template>

<script>
/**
 * @file 按钮组件
 */

const sizeType = {
  small: 'small',
  large: 'large',
}

const size = {
  [sizeType.small]: {
    height: 28,
    borderRadius: 14,
    iconSize: 16,
    fontSize: 12,
  },
  [sizeType.large]: {
    height: 36,
    borderRadius: 18,
    iconSize: 20,
    fontSize: 14,
  },
}

export default {
  props: {
    width: {
      type: [Number, String],
      default: 52,
    },
    size: {
      type: String,
      default: sizeType.small,
    },
    backgroundColor: {
      type: String,
      default: '#3D43EB',
    },
    image: {
      type: Object,
      default() {
        return {}
      },
    },
    color: {
      type: String,
      default: '',
    },
    fontSize: {
      type: [Number, String],
      default: 0,
    },
  },

  computed: {
    formatStyle() {
      let height = size[this.size].height,
        fontsize = this.fontSize ? this.fontSize : size[this.size].fontSize,
        iconSize = size[this.size].iconSize,
        borderRadius = size[this.size].borderRadius

      return {
        buttonStyle: {
          width: `${this.width}px`,
          height: `${height}px`,
          borderRadius: `${borderRadius}px`,
          backgroundColor: this.backgroundColor,
        },
        textStyle: {
          fontSize: `${fontsize}px`,
          color: this.color ? this.color : '#fff',
        },
        iconStyle: {
          width: `${iconSize}px`,
          height: `${iconSize}px`,
        },
      }
    },
  },
}
</script>

<style lang="less">
.widgetui-btn {
  position: relative;
  justify-content: center;
  align-items: center;

  .widgetui-btn-icon {
    margin-right: 4px;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .widgetui-btn-text {
    font-weight: 550;
  }

  .widgetui-btn-mask {
    width: 100%;
    height: 100%;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
  }

  .widgetui-btn-mask:active {
    background-color: rgba(0, 0, 0, 0.12);
  }
}
</style>
