{"name": "SummaryCard", "description": "quickapp official components library", "subversion": {"toolkit": "0.6.8"}, "directories": {"doc": "docs"}, "scripts": {"build": "hap build", "release": "hap release", "start": "hap server --watch", "dev": "vuepress dev docs", "build:docs": "vuepress build docs", "lint": "eslint --ext .js,.ux src", "lint:fix": "eslint --ext .js,.ux src --fix", "pretty": "prettier --write **/*.ux"}, "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{ux,less,js,md}": ["npm run pretty"]}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "semi": false}, "devDependencies": {"@types/quickapp": "npm:quickapp-interface@^1.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.16.0", "eslint-plugin-hybrid": "0.0.5", "hap-toolkit": "^1.9.16", "husky": "^4.3.0", "less": "^3.12.2", "less-loader": "^7.0.1", "lint-staged": "^10.4.0", "prettier": "^2.1.2", "prettier-plugin-quickapp": "^0.1.0", "vuepress": "^1.5.4"}, "dependencies": {"clipboard": "^2.0.6", "vivo-hap-toolkit": "^1.8.3"}, "browserslist": ["chrome 65"]}