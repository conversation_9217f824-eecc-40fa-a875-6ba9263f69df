package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ID3v2ChapterFrameDataTest {

    @Test
    public void should_returnSize_when_unpackFrameData() throws Exception {
        ID3v2ChapterFrameData id3v2ChapterFrameData = new ID3v2ChapterFrameData(true, "111", 1, 1, 2, 2);
        byte[] bytes = Whitebox.invokeMethod(id3v2ChapterFrameData, "packFrameData");
        Whitebox.invokeMethod(id3v2ChapterFrameData, "unpackFrameData", bytes);
        ArrayList<ID3v2Frame> subframe = Whitebox.getInternalState(id3v2ChapterFrameData, "subframes");
        Assert.assertFalse(subframe.size() > 0);
    }
}
