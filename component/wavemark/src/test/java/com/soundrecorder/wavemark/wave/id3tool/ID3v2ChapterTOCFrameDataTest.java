package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.nio.ByteBuffer;
import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ID3v2ChapterTOCFrameDataTest {
    @Test
    public void should_returnNotnull_when_ID3v2ChapterTOCFrameData() {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        Assert.assertEquals(id3v2ChapterTOCFrameData.isRoot, true);
    }

    @Test
    public void should_returnNotnull_when_unpackFrameData() throws InvalidDataException {
        MockedStatic<ByteBufferUtils> mockedStatic = Mockito.mockStatic(ByteBufferUtils.class);
        mockedStatic.when(() -> ByteBufferUtils.extractNullTerminatedString(ByteBuffer.wrap("1023".getBytes()))).thenReturn("1");
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        id3v2ChapterTOCFrameData.unpackFrameData("12".getBytes());
        Assert.assertNull(id3v2ChapterTOCFrameData.id);
        mockedStatic.close();
    }

    @Test
    public void should_returnNotnull_when_addSubframe() {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        id3v2ChapterTOCFrameData.addSubframe("1", id3v2ChapterTOCFrameData);
        Assert.assertEquals(id3v2ChapterTOCFrameData.subframes.size(), 1);
    }

    @Test
    public void should_returnNotnull_when_packFrameData() {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        Assert.assertNotNull(id3v2ChapterTOCFrameData.packFrameData());
    }

    @Test
    public void should_returnNotnull_when_getFlags() throws Exception {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        Assert.assertNotNull(Whitebox.invokeMethod(id3v2ChapterTOCFrameData, "getFlags"));
    }

    @Test
    public void should_returnValue_when_getAndset() throws Exception {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true);
        id3v2ChapterTOCFrameData.setId("1");
        Assert.assertEquals(id3v2ChapterTOCFrameData.getId(), "1");
        String[] strings = new String[]{"1"};
        id3v2ChapterTOCFrameData.setChildren(strings);
        Assert.assertEquals(id3v2ChapterTOCFrameData.getChildren(), strings);
        id3v2ChapterTOCFrameData.setOrdered(false);
        Assert.assertFalse(id3v2ChapterTOCFrameData.isOrdered());
        id3v2ChapterTOCFrameData.setRoot(false);
        Assert.assertFalse(id3v2ChapterTOCFrameData.isRoot());
        id3v2ChapterTOCFrameData.setChilds(strings);
        Assert.assertEquals(id3v2ChapterTOCFrameData.getChilds(), strings);
        id3v2ChapterTOCFrameData.setSubframes(new ArrayList<>());
        Assert.assertNotNull(id3v2ChapterTOCFrameData.getSubframes());
    }

    @Test
    public void should_returnNotnull_when_toString() {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        Assert.assertEquals(id3v2ChapterTOCFrameData.toString(), "ID3v2ChapterTOCFrameData [isRoot=true, isOrdered=true, id=1, children=[1, 2], subframes=[]]");
    }

    @Test
    public void should_returnNotnull_when_hashCode() {
        ID3v2ChapterTOCFrameData id3v2ChapterTOCFrameData = new ID3v2ChapterTOCFrameData(true, true, true, "1", new String[]{"1", "2"});
        Assert.assertNotNull(id3v2ChapterTOCFrameData.hashCode());
    }

}
