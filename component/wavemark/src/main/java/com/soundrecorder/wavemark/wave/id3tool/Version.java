package com.soundrecorder.wavemark.wave.id3tool;

public class Version {
    private static final String VERSION;

    static { // get version from JAR manifest
        String implementationVersion = Version.class.getPackage().getImplementationVersion();
        VERSION = implementationVersion != null ? implementationVersion : "UNKNOWN-SNAPSHOT";
    }

    public static String asString() {
        return getVersion();
    }

    public static String getVersion() {
        return VERSION;
    }
}
