/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.screenstate.ScreenStateLiveData
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.CommonNotificationModel
import com.soundrecorder.notification.R
import com.soundrecorder.notification.base.BaseNotification
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ThirdPartyNotificationTest {

    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_notNull_when_playNameObserver() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(
            notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val playNameObserver: Observer<String> =
            Whitebox.getInternalState(notification, "playNameObserver")
        playNameObserver.onChanged("123")
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_notNull_when_curTimeObserver() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(
            notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val curTimeObserver: Observer<Long> =
            Whitebox.getInternalState(notification, "curTimeObserver")
        curTimeObserver.onChanged(1000)
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_notNull_when_playStatusObserver() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(
            notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val playStatusObserver: Observer<Int> =
            Whitebox.getInternalState(notification, "playStatusObserver")
        playStatusObserver.onChanged(1)
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_return_true_when_observeData() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        notification.observeData()
        Assert.assertEquals(
            true,
            Whitebox.getInternalState<ScreenStateLiveData>(notification, "screenStateLiveData")
                .hasObservers()
        )
    }

    @Test
    fun should_null_when_onRelease() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        notification.onRelease()
        Assert.assertFalse(
            Whitebox.getInternalState<ScreenStateLiveData>(
                notification,
                "screenStateLiveData"
            ).hasObservers()
        )
    }

    @Test
    fun should_equals_when_showNotification() {
        val baseNotification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {

                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        baseNotification.notificationModel =
            CommonNotificationModel().also { it.canJumpIntent = false }
        baseNotification.showNotification(null)
        Assert.assertEquals(
            BaseNotification.REFRESH_STATE_ENABLED,
            Whitebox.getInternalState(baseNotification, "refreshState")
        )
    }

    @Test
    fun should_equals_when_getContentTitle() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setPlayName(MutableLiveData("123"))
        }
        val title: String = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentTitle").first
        Assert.assertEquals("123", title)
    }

    @Test
    fun should_equals_when_getContentText() {
        val notification =
            object : ThirdPartyNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 12) {
                override fun getJumpIntent(): Intent? {
                    return null
                }

                override fun getChannelName(): String? {
                    return null
                }

                override fun getChannelId(): String {
                    return NotificationUtils.PLAYBACK_CID
                }

                override fun getOldChannelId(): String {
                    return NotificationUtils.PLAYBACK_OLD_CID
                }
            }
        notification.notificationModel = CommonNotificationModel()
            .also {
            it.setPlayStatus(MutableLiveData(NotificationModel.RECORD_STATUS_PLAYING))
        }
        val text: String = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentText").first
        Assert.assertEquals(context?.resources?.getString(R.string.play_record), text)
    }
}