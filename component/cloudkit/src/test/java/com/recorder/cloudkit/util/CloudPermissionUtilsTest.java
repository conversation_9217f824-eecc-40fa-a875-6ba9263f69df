package com.recorder.cloudkit.util;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.permission.PermissionUtils;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.utils.CloudPermissionUtils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class CloudPermissionUtilsTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_when_checkCloudRequirePermission() {
        MockedStatic<PermissionUtils> mockedStatic = Mockito.mockStatic(PermissionUtils.class);
        mockedStatic.when(PermissionUtils::hasAllFilePermission).thenReturn(false, true);
        Assert.assertFalse(CloudPermissionUtils.hasCloudRequirePermission());
        Assert.assertTrue(CloudPermissionUtils.hasCloudRequirePermission());
        mockedStatic.close();
    }

    @Test
    public void should_when_veyCloudGrantedStatus() {
        CloudPermissionUtils.setCloudGrantedStatus(mContext);
        Assert.assertTrue(CloudPermissionUtils.isStatementCloudGranted(mContext));
        CloudPermissionUtils.clearCloudGrantedStatus();
        Assert.assertFalse(CloudPermissionUtils.isStatementCloudGranted(mContext));
    }

    @After
    public void tearDown() {
        mContext = null;
    }
}
