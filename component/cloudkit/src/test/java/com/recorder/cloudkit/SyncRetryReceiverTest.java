package com.recorder.cloudkit;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class SyncRetryReceiverTest {

    private static final String RETRY_SYNC_ACTION = "com.multimedia.newsoundrecorder.action.RETRY_SYNC";
    private static final String CLEAR_FAILED_ACTION = "com.multimedia.newsoundrecorder.action.CLEAR_FAILED_ACTION";
    private static final String EXTRA_RETRY_TYPE = "retry_type";

    private Context mContext;
    private SyncRetryReceiver mReceiver;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mReceiver = new SyncRetryReceiver();
    }

    @After
    public void release() {
        mContext = null;
        mReceiver = null;
    }

    @Test
    public void should_trigBackUpNow_when_receiveAction_BACKUP() {
        SyncTriggerManager mockObj = Mockito.mock(SyncTriggerManager.class);
        MockedStatic<SyncTriggerManager> mockedStatic = mockStatic(SyncTriggerManager.class);

        mockedStatic.when(() -> SyncTriggerManager.getInstance(mContext)).thenReturn(mockObj);
        Intent intentAction = new Intent(RETRY_SYNC_ACTION);
        intentAction.putExtra(EXTRA_RETRY_TYPE, SyncTriggerManager.BACKUP);
        mReceiver.onReceive(mContext, intentAction);

        verify(mockObj, times(1)).trigBackupNow();

        mockedStatic.close();
    }

    @Test
    public void should_trigBackUpNow_when_receiveAction_recovery() {
        SyncTriggerManager mockObj = Mockito.mock(SyncTriggerManager.class);
        MockedStatic<SyncTriggerManager> mockedStatic = mockStatic(SyncTriggerManager.class);

        mockedStatic.when(() -> SyncTriggerManager.getInstance(mContext)).thenReturn(mockObj);
        Intent intentAction = new Intent(RETRY_SYNC_ACTION);
        intentAction.putExtra(EXTRA_RETRY_TYPE, SyncTriggerManager.RECOVERY);
        mReceiver.onReceive(mContext, intentAction);

        verify(mockObj, times(1)).trigRecoveryNow(anyInt());

        mockedStatic.close();
    }

    @Test
    public void should_trigBackUpNow_when_receiveAction_backupAfterRecovery() {
        SyncTriggerManager mockObj = Mockito.mock(SyncTriggerManager.class);
        MockedStatic<SyncTriggerManager> mockedStatic = mockStatic(SyncTriggerManager.class);

        mockedStatic.when(() -> SyncTriggerManager.getInstance(mContext)).thenReturn(mockObj);
        Intent intentAction = new Intent(RETRY_SYNC_ACTION);
        intentAction.putExtra(EXTRA_RETRY_TYPE, SyncTriggerManager.RECOVERY);
        mReceiver.onReceive(mContext, intentAction);

        verify(mockObj, times(1)).trigRecoveryNow(anyInt());

        mockedStatic.close();
    }
}
