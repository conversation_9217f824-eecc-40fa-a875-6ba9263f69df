package com.recorder.cloudkit.sync.backup;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;

import android.content.Context;
import android.net.Uri;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.sync.bean.RecordTransferFile;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MD5Utils;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.RecorderDBUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

import java.util.Arrays;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class FileUpLoadUtilTest {
    private static final int PRIORITY_STEP1 = 70;
    private static final int PRIORITY_STEP2 = 60;
    private static final int PRIORITY_STEP3 = 50;
    private static final int PRIORITY_STEP4 = 40;
    private static final int PRIORITY_STEP5 = 30;
    private static final int PRIORITY_STEP6 = 20;

    // CloudKit 4M为临界点，4M下小文件上传，大于4M为大文件断点上传
    private static final int FILE_SIZE_STEP1 = 4 * 1024 * 1024;
    private static final int FILE_SIZE_STEP2 = 20 * 1024 * 1024;
    private static final int FILE_SIZE_STEP3 = 50 * 1024 * 1024;
    private static final int FILE_SIZE_STEP4 = 100 * 1024 * 1024;
    private static final int FILE_SIZE_STEP5 = 500 * 1024 * 1024;

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_empty_when_getUploadFileList() {
        List<RecordTransferFile> unUploadList = FileUpLoadUtil.getUploadFileList(mContext, FILE_SIZE_STEP2, 50);
        Assert.assertTrue(unUploadList.isEmpty());
    }

    @Test
    public void should_notEmpty_when_getUploadFileList() {
        RecorderDBUtil recorderDBUtil = mock(RecorderDBUtil.class);
        MockedStatic<RecorderDBUtil> dbUtilMockedStatic = Mockito.mockStatic(RecorderDBUtil.class);
        MockedStatic<FileUtils> fileUtilsMockedStatic = Mockito.mockStatic(FileUtils.class);
        MockedStatic<MediaDBUtils> mediaDBUtilsMockedStatic = Mockito.mockStatic(MediaDBUtils.class);
        MockedStatic<MD5Utils> md5UtilsMockedStatic = Mockito.mockStatic(MD5Utils.class);

        Record record = new Record();
        record.setFileSize(12344);
        record.setUuid("11");
        record.setDirty(3);
        Record record1 = new Record();
        record1.setFileSize(6999);
        record1.setUuid("22");
        record1.setRelativePath("Music/Recordings/Standard Recordings");
        record1.setDisplayName("标准.mp3");
        record1.setDirty(1);

        dbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(mContext)).thenReturn(recorderDBUtil);
        dbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(mContext).getDirtyDataForBatchUploadFile()).thenReturn(Arrays.asList(record, record1));
        mediaDBUtilsMockedStatic.when(() -> MediaDBUtils.getMediaUriForRecord(any())).thenReturn(mock(Uri.class));
        fileUtilsMockedStatic.when(() -> FileUtils.isFileExist(anyString(), anyString())).thenReturn(true);
        md5UtilsMockedStatic.when(() -> MD5Utils.getMD5((Uri) any())).thenReturn("d54de14da1");

        List<RecordTransferFile> unUploadList = FileUpLoadUtil.getUploadFileList(mContext, FILE_SIZE_STEP2, 50);
        Assert.assertFalse(unUploadList.isEmpty());

        md5UtilsMockedStatic.close();
        fileUtilsMockedStatic.close();
        mediaDBUtilsMockedStatic.close();
        dbUtilMockedStatic.close();
    }

    @Test
    public void should_when_getPriorityBySize() {
        Assert.assertEquals(0, FileUpLoadUtil.getPriorityBySize(0));
        Assert.assertEquals(PRIORITY_STEP1, FileUpLoadUtil.getPriorityBySize(3));
        Assert.assertEquals(PRIORITY_STEP2, FileUpLoadUtil.getPriorityBySize(FILE_SIZE_STEP1));
        Assert.assertEquals(PRIORITY_STEP3, FileUpLoadUtil.getPriorityBySize(FILE_SIZE_STEP2));
        Assert.assertEquals(PRIORITY_STEP4, FileUpLoadUtil.getPriorityBySize(FILE_SIZE_STEP3));
    }

}
