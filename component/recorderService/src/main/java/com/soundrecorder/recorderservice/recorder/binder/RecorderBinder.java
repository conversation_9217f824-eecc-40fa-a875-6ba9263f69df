/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderBinder.java
 Description:
 Version: 1.0
 Date: 2019.08.28
 Author: mao_hongyu
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2019.08.28 1.0 create
 */

package com.soundrecorder.recorderservice.recorder.binder;

import android.os.Binder;

import com.soundrecorder.recorderservice.RecorderService;

public class RecorderBinder extends Binder {

    private RecorderService mRecorderService;

    public RecorderBinder(RecorderService mRecorderService) {
        this.mRecorderService = mRecorderService;
    }

    public void cleanStatus() {
        mRecorderService = null;
    }

    public RecorderService getService() {
        return mRecorderService;
    }
}
