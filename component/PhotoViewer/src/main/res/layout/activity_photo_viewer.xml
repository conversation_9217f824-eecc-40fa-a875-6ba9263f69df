<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:forceDarkAllowed="false"
    tools:background="#FF000000"
    tools:ignore="UnusedAttribute"
    tools:viewBindingIgnore="true">

    <com.photoviewer.ui.PhotoViewerView
        android:id="@+id/photoViewerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/singleSaveRootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0"
        android:visibility="gone"
        tools:alpha="1"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/pop_picture_title_height"
            android:layout_gravity="center|top"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:text="@string/tips_change_picture_mark"
            android:textColor="#FFFFFFFF"
            android:textFontWeight="500"
            android:textSize="@dimen/pop_picture_title_font"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageButton
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/pop_button_width"
            android:layout_height="@dimen/pop_button_width"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="43dp"
            android:layout_marginBottom="@dimen/pop_button_margin_bottom"
            android:background="@android:color/transparent"
            android:contentDescription="@string/cancel"
            android:foreground="@android:color/transparent"
            android:scaleType="center"
            android:src="@drawable/pv_base_icon_cancel" />

        <ImageButton
            android:id="@+id/btnOk"
            android:layout_width="@dimen/pop_button_width"
            android:layout_height="@dimen/pop_button_width"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="43dp"
            android:layout_marginBottom="@dimen/pop_button_margin_bottom"
            android:background="@android:color/transparent"
            android:contentDescription="@string/confirm"
            android:foreground="@drawable/recommended_text_ripple_bg"
            android:scaleType="center"
            android:src="@drawable/pv_base_icon_ok" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/layoutHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:alpha="0"
        android:background="#8C000000"
        android:visibility="gone"
        tools:alpha="1"
        tools:visibility="visible">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/coui_transparence"
            app:navigationContentDescription="@string/cancel"
            app:navigationIcon="@drawable/pv_icon_back" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/layoutFooter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:alpha="0"
        android:background="#8C000000"
        android:visibility="gone"
        tools:alpha="1"
        tools:visibility="visible">

        <com.coui.appcompat.bottomnavigation.COUINavigationView
            android:id="@+id/navigationBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:couiNaviIconTint="@color/coui_color_white"
            app:couiToolNavigationViewBg="@color/coui_transparence"
            app:couiNaviMenu="@menu/menu_navigation"
            app:couiNaviTextColor="@color/coui_color_white" />
    </FrameLayout>
</merge>