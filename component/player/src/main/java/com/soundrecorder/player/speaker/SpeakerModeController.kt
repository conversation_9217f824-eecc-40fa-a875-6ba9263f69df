package com.soundrecorder.player.speaker

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.player.R

class SpeakerModeController : ToDoInSpeakerReceiver {

    var mSpeakerUiMode: MutableLiveData<Int> = MutableLiveData()

    /**听筒/扬声器模式：true为扬声器；false为听筒*/
    var mIsSpeakerOn: MutableLiveData<Boolean> = MutableLiveData()

    /**给播放器下发一个命令，暂停播放-这里获取不到播放器,为一次性指令，直到下次setValue*/
    var mPlayerCommand: CustomMutableLiveData<Int> = CustomMutableLiveData()

    var mMainHandler: Handler? = Handler(Looper.getMainLooper())


    companion object {
        const val PLAYER_COMMAND_DEFAULT = 0
        const val PLAYER_COMMAND_PAUSE = 0
        const val PLAYER_SPEAKER_BROWSE = 0
        const val PLAYER_SPEAKER_PLAYBACK = 1
        val TAG = "SpeakerModeController"
    }


    fun loadInitMode() {
        val preSpeakerSpStatus = SpeakerStateManager.getInstance().speakerSp
        val isHeadSetOn = SpeakerStateManager.isAnyHeadsetOn()
        val uiState = getUIStateForSpStateAndHeadSetState(preSpeakerSpStatus, isHeadSetOn)
        DebugUtil.i(
            TAG,
            "loadInitMode: preSpeakerSpStatus $preSpeakerSpStatus, isHeadSetOn: $isHeadSetOn, uiState $uiState")
        mSpeakerUiMode.postValue(uiState)
        mIsSpeakerOn.postValue(preSpeakerSpStatus == SpeakerStateManager.SP_SPEAKER_ON)
    }


    fun performSpeakerMenuItemClick(source: Int) {
        DebugUtil.d(TAG, " onMenuItemClick is in !")
        if (ClickUtils.isQuickClick()) {
            return
        }
        val preSpeakerSpStatus = SpeakerStateManager.getInstance().speakerSp
        CoroutineUtils.ioToMain({ SpeakerStateManager.isAnyHeadsetOn() }, {
            /*could change the streamType on androidS or later for androids cant receive headseton*/
            if (!BaseUtil.isAndroidSOrLater && it) {
                ToastManager.showShortToast(BaseApplication.getAppContext(),
                    BaseApplication.getAppContext()
                        .getString(R.string.disconnect_the_device_and_switch_the_playback_mode)
                )
            } else {
                val nextSpeakerSpStatus: Int
                if (preSpeakerSpStatus == SpeakerStateManager.SP_SPEAKER_ON) {
                    nextSpeakerSpStatus = SpeakerStateManager.SP_SPEAKER_OFF
                    SpeakerStateManager.getInstance().setSpeakerOffSp()
                    SpeakerStateManager.getInstance().setIsHeadSetPluged(true)
                    /*赋值必须放到sp后*/
                    mIsSpeakerOn.value = false
                    if (source == PLAYER_SPEAKER_BROWSE) {
                        BuryingPoint.addRecordPlayDeviceBrowse(RecorderUserAction.VALUE_PLAY_SPEAKER_OFF)
                    } else {
                        BuryingPoint.addRecordPlayDevice(RecorderUserAction.VALUE_PLAY_SPEAKER_OFF)
                    }
                    ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.talk_back_handset_play)
                } else {
                    nextSpeakerSpStatus = SpeakerStateManager.SP_SPEAKER_ON
                    SpeakerStateManager.getInstance().setSpeakerOnSp()
                    SpeakerStateManager.getInstance().setIsHeadSetPluged(false)
                    mIsSpeakerOn.value = true
                    if (source == PLAYER_SPEAKER_BROWSE) {
                        BuryingPoint.addRecordPlayDeviceBrowse(RecorderUserAction.VALUE_PLAY_SPEAKER_ON)
                    } else {
                        BuryingPoint.addRecordPlayDevice(RecorderUserAction.VALUE_PLAY_SPEAKER_ON)
                    }
                    ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.talk_back_speaker_play)
                }

                val uiState = getUIStateForSpStateAndHeadSetState(nextSpeakerSpStatus, it)
                mSpeakerUiMode.value = uiState
            }
        })
    }


    override fun doWithBlueTooth(status: Int) {
        DebugUtil.d(TAG, " doWithBlueTooth state: $status")
        val sm = SpeakerStateManager.getInstance()
        when (status) {
            SpeakerReceiver.HEADSET_CONNECTED -> {
                sm.setIsHeadSetPluged(true)
                updateUiWhenHeadSetPlugInOrOut(true)
            }
            SpeakerReceiver.HEADSET_DISCONNECTED -> if (!SpeakerStateManager.isWiredHeadsetOn()) {
                sm.setIsHeadSetPluged(false)
                updateUiWhenHeadSetPlugInOrOut(false)
            }
        }
    }

    override fun doWithWired(state: Int) {
        DebugUtil.d(TAG, " doWithWired state: $state")
        val sm = SpeakerStateManager.getInstance()
        when (state) {
            SpeakerReceiver.HEADSET_CONNECTED -> {
                sm.setIsHeadSetPluged(true)
                updateUiWhenHeadSetPlugInOrOut(true)
            }
            SpeakerReceiver.HEADSET_DISCONNECTED -> if (!SpeakerStateManager.isAnyHeadsetOn()) {
                sm.setIsHeadSetPluged(false)
                updateUiWhenHeadSetPlugInOrOut(false)
            }
        }
    }

    override fun doWithReceiveNoisy() {
        DebugUtil.d(TAG, " doWithReceiveNoisy! ")
        val sm = SpeakerStateManager.getInstance()
        pausePlayWhenHeadSetPlugOut()
        sm.setIsHeadSetPluged(false)
        updateUiWhenHeadSetPlugInOrOut(false)
    }

    private fun updateUiWhenHeadSetPlugInOrOut(isHeadSetOn: Boolean) {
        val preSpeakerSpStatus = SpeakerStateManager.getInstance().speakerSp
        DebugUtil.i(TAG, "updateUiWhenHeadSetPlugInOrOut: preSpeakerSpStatus $preSpeakerSpStatus, isHeadSetOn: $isHeadSetOn")
        val uiState: Int = getUIStateForSpStateAndHeadSetState(preSpeakerSpStatus, isHeadSetOn)
        mSpeakerUiMode.postValue(uiState)
        mIsSpeakerOn.postValue(!isHeadSetOn)
    }

    private fun getUIStateForSpStateAndHeadSetState(spState: Int, headSetPlugIn: Boolean): Int {
        var result = SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET
        result = if (headSetPlugIn) {
            if (spState == SpeakerStateManager.SP_SPEAKER_ON) {
                SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET
            } else {
                SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET
            }
        } else {
            if (spState == SpeakerStateManager.SP_SPEAKER_ON) {
                SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET
            } else {
                SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET
            }
        }
        return result
    }

    private fun pausePlayWhenHeadSetPlugOut() {
        mMainHandler?.post(Runnable {
            // 下发暂停指令
            mPlayerCommand.setValue(PLAYER_COMMAND_PAUSE)
        })
    }
}