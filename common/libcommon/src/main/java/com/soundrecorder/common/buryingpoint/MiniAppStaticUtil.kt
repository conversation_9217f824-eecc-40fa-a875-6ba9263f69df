/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniAppStaticUtil
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

object MiniAppStaticUtil {
    private const val EVENT_GROUP_MINI_APP = "mini_app"
    private const val EVENT_ID_MINI_APP_CONTINUE = "miniapp_continue_action"
    private const val EVENT_ID_ENTER_MINI_APP = "enter_mini_recorder"

    /**
     * 对应value
     * {"record_default":"录制默认页面","recording":"录制中","upgrade_notice":"升级弹窗",
     * "user_notice":"用户须知","permission_dialog":"权限弹窗"}
     */
    private const val EVENT_KEY_MINI_APP_CONTINUE_FROM = "from"
    const val MINI_APP_CONTINUE_FROM_DEFAULT = "record_default"
    const val MINI_APP_CONTINUE_FROM_RECORDING = "recording"
    /*未使用，统一统计在MINI_APP_CONTINUE_FROM_USER_NOTICE中*/
    const val MINI_APP_CONTINUE_FROM_UPGRADE_NOTICE = "upgrade_notice"
    const val MINI_APP_CONTINUE_FROM_USER_NOTICE = "user_notice"
    const val MINI_APP_CONTINUE_FROM_PERMISSION_DIALOG = "permission_dialog"

    /**
     * 统计进入外屏miniapp的次数
     */
    @JvmStatic
    fun addEnterMiniAppEvent() {
        val eventInfo = HashMap<String?, String?>()
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_MINI_APP, EVENT_ID_ENTER_MINI_APP, eventInfo, false)
        DebugUtil.i("MiniAppStaticUtil", "addEnterMiniAppEvent")
    }

    /**
     * 展开手机接续来源
     * @param from 接续来源
     * @see  MINI_APP_CONTINUE_FROM_DEFAULT or MINI_APP_CONTINUE_FROM_RECORDING or
     * MINI_APP_CONTINUE_FROM_UPGRADE_NOTICE or MINI_APP_CONTINUE_FROM_USER_NOTICE or
     * MINI_APP_CONTINUE_FROM_PERMISSION_DIALOG
     */
    @JvmStatic
    fun addMiniAppContinueEvent(from: String) {
        val eventInfo = HashMap<String?, String?>()
        eventInfo[EVENT_KEY_MINI_APP_CONTINUE_FROM] = from
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), EVENT_GROUP_MINI_APP, EVENT_ID_MINI_APP_CONTINUE, eventInfo, false)
        DebugUtil.i("MiniAppStaticUtil", "addMiniAppContinueEvent")
    }
}