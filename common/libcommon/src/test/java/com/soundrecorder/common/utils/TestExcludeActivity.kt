/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TestExcludeActivity
 Description:
 Version: 1.0
 Date: 2022/9/23
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/23 1.0 create
 */

package com.soundrecorder.common.utils

import android.app.Activity
import com.soundrecorder.common.task.ExcludeActivityTask

class TestExcludeActivity : Activity(), ExcludeActivityTask {
    override fun hasExclude(): <PERSON><PERSON><PERSON> {
        return true
    }
}
