package com.soundrecorder.common.fileoperator.rename;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;

import android.app.Activity;
import android.net.Uri;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.R;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;

import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;

import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowCursorHelper;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class NameFileDialogUtilTest {
    public static final int DIALOG_TYPE_RENAME = 1;
    public static final int DIALOG_TYPE_RECORD = 2;
    public static final int DIALOG_TYPE_CUT = 3;
    public static final int DIALOG_TYPE_MARK = 4;
    private static final String SUFFIX = ".mp3";
    private static final String VALUE_DISPLAY_NAME = "display_name.mp3";
    private static final String VALUE_RESULT_NAME = "result_name";
    private static final String VALUE_RELATIVE_PATH = "relativepath";

    private static final String VALUE_CORE_NAME = "display_name";
    private static final String VALUE_FULL_NAME = "testpath\\display_name.mp3";
    private static final String VALUE_CORE2FULL_NAME = "testpath\\result_name.mp3";

    private ActivityController<Activity> mController;
    private NameFileDialogUtil instance;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(Activity.class);
        instance = new NameFileDialogUtil(Constants.REQUEST_CODE_RENAME);
    }

    @Test
    @Config(shadows = {ShadowCursorHelper.class})
    public void should_returnType_when_onPositive() {
        Activity activity = mController.get();
        Record record = mock(Record.class);
        Assert.assertEquals(R.string.error_none_filename, instance.onPositive(activity, DIALOG_TYPE_RENAME, "", record));
        Assert.assertEquals(R.string.notify_illegal_emoji_new, instance.onPositive(activity, DIALOG_TYPE_RENAME, ".1", record));
        assertEquals(-1, instance.onPositive(activity, DIALOG_TYPE_RECORD, "", record));
        Assert.assertEquals(R.string.error_none_filename, instance.onPositive(activity, DIALOG_TYPE_CUT, "", record));
        Assert.assertEquals(R.string.notify_illegal_emoji_new, instance.onPositive(activity, DIALOG_TYPE_CUT, ".1", record));
        assertEquals(-1, instance.onPositive(activity, DIALOG_TYPE_MARK, "", record));
    }

    @Test
    @Config(shadows = {ShadowBaseUtils.class})
    public void should_correct_when_renamePositive_aboveQ() throws Exception {
        Activity activity = mController.get();
        Record record = new Record();
        record.setId(1001L);
        record.setRelativePath(VALUE_RELATIVE_PATH);
        record.setDisplayName(VALUE_DISPLAY_NAME);
        record.setMimeType("mp3");
        MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
        MockedStatic<MediaDBUtils> mediaDBUtilsMockedStatic = mockStatic(MediaDBUtils.class);
        fileUtilsMockedStatic.when(() -> FileUtils.isFileExist(VALUE_RELATIVE_PATH, VALUE_RESULT_NAME + SUFFIX)).thenReturn(false, true, false);
        fileUtilsMockedStatic.when(() -> FileUtils.isFileExist(any(Uri.class))).thenReturn(false, true, true);
        mediaDBUtilsMockedStatic.when(() -> MediaDBUtils.getRecordFromMediaByUriId(any(Uri.class))).thenReturn(record);
        mediaDBUtilsMockedStatic.when(() -> MediaDBUtils.genUri(1001L)).thenReturn(spy(Uri.class));
        mediaDBUtilsMockedStatic.when(() -> MediaDBUtils.rename(any(Uri.class), anyString(), anyString(), anyString())).thenReturn(1);
        int result = Whitebox.invokeMethod(instance, "renamePositive", activity, record, VALUE_RESULT_NAME);
        Assert.assertEquals(R.string.record_file_not_exist, result);
        result = Whitebox.invokeMethod(instance, "renamePositive", activity, record, VALUE_RESULT_NAME);
        Assert.assertEquals(R.string.error_title, result);
        result = Whitebox.invokeMethod(instance, "renamePositive", activity, record, VALUE_RESULT_NAME);
        assertEquals(-1, result);
        fileUtilsMockedStatic.close();
        mediaDBUtilsMockedStatic.close();
    }

    @Test
    @Ignore
    public void should_correct_when_full2Core() throws Exception {
        String result = Whitebox.invokeMethod(instance, "full2Core", VALUE_FULL_NAME);
        assertEquals(VALUE_CORE_NAME, result);
    }

    @Test
    @Ignore
    public void should_correct_when_core2Full() throws Exception {
        String result = Whitebox.invokeMethod(instance, "core2Full", VALUE_RESULT_NAME, VALUE_FULL_NAME);
        assertEquals(VALUE_CORE2FULL_NAME, result);
    }

    @Test
    @Config(shadows = ShadowBaseUtils.class)
    public void should_correct_when_clipPositive_aboveQ() throws Exception {
        Activity activity = mController.get();
        Record record = new Record();
        record.setId(1001L);
        record.setRelativePath(VALUE_RELATIVE_PATH);
        record.setDisplayName(VALUE_DISPLAY_NAME);
        MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
        String toName = VALUE_RESULT_NAME + SUFFIX;
        fileUtilsMockedStatic.when(() -> FileUtils.isFileExist(VALUE_RELATIVE_PATH, toName)).thenReturn(false, true, false);
        fileUtilsMockedStatic.when(() -> FileUtils.isFileExist(VALUE_RELATIVE_PATH, VALUE_DISPLAY_NAME)).thenReturn(false, true, true);
        int result = Whitebox.invokeMethod(instance, "clipPositive", activity, record, VALUE_RESULT_NAME);
        Assert.assertEquals(R.string.record_file_not_exist, result);
        result = Whitebox.invokeMethod(instance, "clipPositive", activity, record, VALUE_RESULT_NAME);
        Assert.assertEquals(R.string.error_title, result);
        result = Whitebox.invokeMethod(instance, "clipPositive", activity, record, VALUE_RESULT_NAME);
        assertEquals(-1, result);
        fileUtilsMockedStatic.close();
    }

    @After
    public void tearDown() {
        instance = null;
        mController = null;
    }

}

