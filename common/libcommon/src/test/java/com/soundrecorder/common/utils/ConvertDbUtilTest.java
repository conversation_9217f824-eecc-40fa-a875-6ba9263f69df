package com.soundrecorder.common.utils;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.ConvertStatus;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertDbUtilTest {

    private MockedStatic<BaseApplication> mMockBaseApplication;
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mMockBaseApplication = Mockito.mockStatic(BaseApplication.class);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @After
    public void teardown() {
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
        mContext = null;
    }

    @Test
    public void should_returnResult_when_insertCheckRecordId() {
        Assert.assertFalse(ConvertDbUtil.insertCheckRecordId(null));
        ConvertRecord record = Mockito.mock(ConvertRecord.class);
        Assert.assertTrue(ConvertDbUtil.insertCheckRecordId(record));
    }

    @Test
    public void should_returnResult_when_insert() {
        Assert.assertFalse(ConvertDbUtil.insert(null));
        ConvertRecord record = Mockito.mock(ConvertRecord.class);
        Assert.assertTrue(ConvertDbUtil.insert(record));
    }

    @Test
    @Ignore
    public void should_returnResult_when_deleteByRecordId() {
        Assert.assertEquals(ConvertDbUtil.deleteByRecordId(11l), 0);
    }

    @Test
    @Ignore
    public void should_returnResult_when_updateConvertRecordWithoutUploadRecords() {
        Assert.assertEquals(ConvertDbUtil.updateConvertRecordWithoutUploadRecords(null), 0);
        Assert.assertEquals(ConvertDbUtil.updateConvertRecordWithoutUploadRecords(new ConvertRecord()), 0);
    }

    @Test
    @Ignore
    public void should_returnResult_when_updateConvertRecordWithoutUploadRecordsOnRecordId() {
        Assert.assertEquals(ConvertDbUtil.updateConvertRecordWithoutUploadRecordsOnRecordId(null), 0);
        Assert.assertEquals(ConvertDbUtil.updateConvertRecordWithoutUploadRecordsOnRecordId(new ConvertRecord()), 0);
    }

    @Test
    public void should_returnResult_when_updateConvertRecordWithUploadRecords() {
        Assert.assertEquals(ConvertDbUtil.updateConvertRecordWithUploadRecords(null), 0);
    }

    @Test
    public void should_returnResult_when_updateRecordIdByMediaPath() {
        Assert.assertEquals(ConvertDbUtil.updateRecordIdByMediaPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3",
                1), 0);
    }

    @Test
    @Ignore
    public void should_returnNotnull_when_selectById() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        ConvertDbUtil.insert(convertRecord);
        Assert.assertNotNull(ConvertDbUtil.selectById(1));
    }

    @Test
    public void should_returnNotnull_when_selectByIds() {
        List<Long> ids = new ArrayList<>();
        ids.add(1l);
        Assert.assertNotNull(ConvertDbUtil.selectByIds(ids));
        Assert.assertNotNull(ConvertDbUtil.selectAll());
    }

    @Test
    public void should_returnFalse_when_somemethold() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        ConvertDbUtil.insert(convertRecord);
        Assert.assertFalse(ConvertDbUtil.checkAlreadyConvertComplete(1));
        Assert.assertFalse(ConvertDbUtil.checkAlreadyConvertComplete(new ConvertRecord()));
    }

    @Test
    public void should_returnFalse_when_getConvertProgressFromConvertRecord() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertEquals(ConvertDbUtil.getConvertProgressFromConvertRecord(convertRecord), 0);
    }

    @Test
    public void should_returnFalse_when_updateAllUrlByRecordId() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertTrue(ConvertDbUtil.updateAllUrlByRecordId(1, "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3"));
    }

    @Test
    public void should_returnFalse_when_updateConvertStatusByRecordId() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertTrue(ConvertDbUtil.updateAllUrlByRecordId(1, "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3"));
        Assert.assertTrue(ConvertDbUtil.updateConvertStatusByRecordId(1, new ConvertStatus()));
        Assert.assertTrue(ConvertDbUtil.updateTaskIdByRecordId(1, "11"));
    }

    @Test
    public void should_returnFalse_when_updateTaskIdAndUploadAllUrl() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertTrue(ConvertDbUtil.updateTaskIdAndUploadAllUrl(1,
                "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", "11"));
    }

    @Test
    public void should_returnFalse_when_updateCompleteAndFilePathByRecordId() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertTrue(ConvertDbUtil.updateCompleteAndFilePathByRecordId(1,
                "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3"));
    }

    @Test
    @Ignore
    public void should_returnFalse_when_update() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertEquals(ConvertDbUtil.update(convertRecord), 0);
    }

    @Test
    @Ignore
    public void should_returnSize_when_updateConvertStatusOnSendOCS() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setUploadRecordList(new ArrayList<>());
        convertRecord.setChunkName("test");
        ConvertDbUtil.insert(convertRecord);
        Assert.assertEquals(ConvertDbUtil.updateConvertStatusOnSendOCS(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateConvertStatusOnConvert(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateKey(1, "1"), 1);
        Assert.assertEquals(ConvertDbUtil.updateUploadId(1, "1"), 1);
        Assert.assertEquals(ConvertDbUtil.updatePartCount(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateConvertTaskId(1, "1"), 1);
        Assert.assertEquals(ConvertDbUtil.updateAllUrl(1, "1"), 1);
        Assert.assertEquals(ConvertDbUtil.updateHistoryRoleName(1, "1"), 1);
        Assert.assertEquals(ConvertDbUtil.updateCanShowSpeakerRole(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateServerPlanCode(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateSpeakerRoleIsShowing(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateSpeakerRoleOriginalNumber(1, 1), 1);
        Assert.assertEquals(ConvertDbUtil.updateSpeakerRoleHasFirstshow(1, 1), 1);
    }

    @Test
    @Ignore
    public void should_returnNotnull_when_queryConvertSuccessCount() {
        ConvertRecord convertRecord = new ConvertRecord(1);
        convertRecord.setCompleteStatus(ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE);
        convertRecord.setUploadRecordList(new ArrayList<>());
        ConvertRecord convertRecord1 = new ConvertRecord(2);
        convertRecord1.setUploadRecordList(new ArrayList<>());
        ConvertDbUtil.insert(convertRecord);
        ConvertDbUtil.insert(convertRecord1);
        Assert.assertEquals(1, ConvertDbUtil.queryConvertSuccessCount());
    }
}
