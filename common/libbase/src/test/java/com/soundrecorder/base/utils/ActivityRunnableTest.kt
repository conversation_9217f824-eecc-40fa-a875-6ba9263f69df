/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ActivityRunnableTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ActivityRunnableTest {

    @Test
    fun should_success_when_run() {
        val testActivity = object : BaseActivity() {
            override fun navigationBarColor(): Int {
                return -1
            }
        }
        val runnable = object : ActivityRunnable<BaseActivity>("TestActivity", testActivity) {

            override fun run(activity: BaseActivity) {
                Assert.assertEquals(-1, activity.navigationBarColor())
            }
        }
        runnable.run()
    }
}